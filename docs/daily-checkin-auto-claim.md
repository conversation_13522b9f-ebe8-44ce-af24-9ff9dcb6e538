# Daily Check-in Auto Claim Feature

## Overview

This feature automatically claims points for the DAILY_CHECKIN task when users log into the system for the first time each day. The implementation leverages NATS messaging to process login events and trigger the daily check-in task completion.

## Architecture

### Components

1. **NATS Login Event Stream**: Listens to `dex.user.login` subject
2. **Login Event Handler**: Processes login events and triggers daily check-in
3. **Activity Cashback System**: Manages task completion and points claiming
4. **Background Job Manager**: Handles the actual task processing

### Message Flow

```
User Login → NATS Event (dex.user.login) → Login Handler → Background Job Manager → Daily Check-in Task → Points Claimed
```

## Implementation Details

### NATS Configuration

- **Stream**: `dex_user`
- **Subjects**: 
  - `dex.user.wallet.new` (existing - user creation)
  - `dex.user.login` (new - user login)
- **Consumer**: `AgentLoginConsumer` (for login events)

### Message Format

The login event message follows this JSON structure:

```json
{
  "userId": "01990f58-034c-7664-a1e9-32f12d39b2ca",
  "loginMethod": "wallet",
  "timestamp": "2025-09-04T11:50:13.985Z"
}
```

### Key Files Modified/Created

1. **`internal/nats/model.go`**
   - Added `DexUserLoginEvent` struct
   - Added `DexUserLoginSubject` constant

2. **`internal/service/dex_user/dex_user_subscriber.go`**
   - Enhanced to handle both user creation and login events
   - Added `startDexUserLoginSubscriber()` method
   - Added `handleDexUserLoginMessage()` method
   - Added `processUserLogin()` method

3. **`internal/task/user_login_task.go`** (new)
   - Contains `ConsumeUserLoginEvent()` handler
   - Processes login events for daily check-in

4. **`internal/task/event.go`**
   - Added `UserLoginSubject` constant
   - Added `UserLoginEvent` struct

5. **`internal/app/worker_sync_user.go`**
   - Added login event handler to subjects map

6. **`internal/initializer/nats.go`**
   - Updated to pass BackgroundJobManager to DexUserSubscriberService

## How It Works

### Daily Check-in Logic

1. **User logs in** to the system (first time each day)
2. **Login event published** to NATS subject `dex.user.login`
3. **Event handler receives** the login message
4. **Background Job Manager** processes the login event
5. **Daily Check-in task** is automatically completed
6. **Points are claimed** immediately upon task completion

### Duplicate Prevention

The system prevents duplicate daily check-ins through:

- **Daily completion tracking**: Checks if user has already completed daily check-in today
- **Database constraints**: Ensures only one completion per user per day
- **Task verification**: Validates task completion before awarding points

### Error Handling

- **Invalid user ID**: Logs error and skips processing
- **System not initialized**: Gracefully handles when Activity Cashback system is not ready
- **Database errors**: Logs errors but doesn't crash the system
- **NATS connection issues**: Implements retry logic with exponential backoff

## Configuration

### Environment Variables

The feature uses existing NATS configuration:

```env
MEME_NATS_URL=nats://your-nats-server:4222
MEME_NATS_USER=your-username
MEME_NATS_PASS=your-password
```

### Task Configuration

The DAILY_CHECKIN task is configured in the Activity Cashback system:

- **Task ID**: `DAILY_CHECKIN`
- **Category**: `daily`
- **Frequency**: Daily reset at UTC 00:00
- **Points**: Configurable (default varies by user tier)

## Testing

### Manual Testing

Use the provided test script:

```bash
go run test_login_event.go
```

This will:
1. Initialize the system
2. Publish a test login event
3. Verify the daily check-in is processed

### Integration Testing

The feature integrates with:
- **User authentication system**: Receives login events
- **Activity Cashback system**: Processes task completion
- **Points system**: Awards points automatically
- **Database**: Tracks completion history

## Monitoring

### Logs

Key log messages to monitor:

```
INFO: Processing dex user login event (user_id=..., login_method=...)
INFO: Daily check-in completed (user_id=...)
INFO: Points awarded for task completion (user_id=..., points=...)
```

### Error Logs

```
ERROR: Failed to process user login (user_id=..., error=...)
WARN: Background job manager not available, skipping login processing
WARN: Activity Cashback system not initialized, ignoring event
```

## Benefits

1. **Automatic Points Claiming**: Users don't need to manually claim daily check-in points
2. **Improved User Experience**: Seamless integration with login flow
3. **Consistent Engagement**: Encourages daily platform usage
4. **Scalable Architecture**: Handles high-volume login events efficiently
5. **Fault Tolerant**: Graceful error handling and recovery

## Future Enhancements

1. **Login Streak Tracking**: Track consecutive login days
2. **Bonus Points**: Award extra points for login streaks
3. **Time-based Bonuses**: Different points based on login time
4. **Multi-platform Support**: Handle login events from different platforms
5. **Analytics Integration**: Track login patterns and engagement metrics
