# Dex User Subscriber Service

## Overview

The Dex User Subscriber Service is responsible for listening to NATS Stream "dex_user" and automatically creating users in the xbit-agent system when new users are registered in the external authentication system.

## Features

- **Automatic User Creation**: Creates users with default Level 1 and Activity Cashback settings
- **Race Condition Handling**: Uses database transactions to prevent duplicate user creation
- **Wallet Management**: Automatically creates user wallets from the event data
- **Error Handling**: Gracefully handles invalid data and continues processing
- **Graceful Shutdown**: Properly cleans up subscriptions on application shutdown

## Architecture

### Components

1. **DexUserSubscriberService**: Main service that handles NATS subscription and message processing
2. **DexUserEvent Model**: Data structure for parsing NATS messages
3. **AgentReferralService**: Used for user creation and wallet management

### NATS Configuration

- **Stream**: `dex_user`
- **Subject**: `dex.user.wallet.new`
- **Consumer**: `AgentSyncUserConsumer`

## Message Format

The service expects messages in the following JSON format:

```json
{
  "userId": "0199085a-9ddf-7368-85b2-ce5ae8b37b5f",
  "wallets": [
    {
      "id": "0199085a-a69a-7d17-b388-0bc135604922",
      "chain": "EVM",
      "walletAddress": "******************************************",
      "walletAccountId": "7d3d71d3-8da7-412c-9c8f-168cb0ad44e3",
      "walletId": "395448f9-1249-50ea-831b-490eed4774c0",
      "hdPath": "m/44'/60'/0'/0/0",
      "name": "Main Account",
      "createdAt": "2025-09-02T02:56:22.682Z"
    }
  ]
}
```

### Supported Chains

- `EVM`: Ethereum Virtual Machine compatible chains
- `SOLANA`: Solana blockchain
- `TRON`: Tron blockchain
- `ARB`: Arbitrum blockchain

## User Creation Logic

When a new user event is received:

1. **Validation**: Validates the user ID format
2. **Duplicate Check**: Checks if user already exists in database
3. **User Creation**: Creates new user with:
   - Default Agent Level: Level 1
   - Activity Cashback enabled
   - Empty email and invitation code (to be set later)
4. **Referral Snapshot**: Creates initial referral snapshot with zero counts
5. **Wallet Creation**: Creates user wallets for valid wallet data

### Race Condition Handling

The service uses database transactions to handle the rare case where:
- User registers with referral/invitation code in xbit-agent
- User also gets created via external auth system
- Both events arrive simultaneously

The transaction ensures only one user record is created, and the existing user is preserved if already exists.

## Configuration

The service is automatically initialized when the application starts. Configuration is handled through:

- **NATS Connection**: Uses `global.GVA_NATS_MEME` client
- **Database**: Uses `global.GVA_DB` for user creation
- **Logging**: Uses `global.GVA_LOG` for structured logging

## Error Handling

### Message Processing Errors

- **Invalid JSON**: Logs error and acknowledges message
- **Invalid User ID**: Logs error and acknowledges message
- **Database Errors**: Logs error and does NOT acknowledge (allows retry)

### Wallet Creation Errors

- **Invalid Chain**: Logs warning and skips wallet
- **Empty Address**: Skips wallet creation
- **Database Error**: Logs error but continues with user creation

## Monitoring and Logging

The service provides structured logging for:

- Service startup/shutdown
- Message processing
- User creation success/failure
- Wallet creation issues
- Error conditions

### Log Levels

- **INFO**: Service lifecycle, successful operations
- **WARN**: Invalid wallet data, unsupported chains
- **ERROR**: Database errors, message processing failures
- **DEBUG**: Raw message content, detailed processing steps

## Testing

The service includes comprehensive tests:

- **Unit Tests**: Chain type parsing, error handling
- **Integration Tests**: Service initialization, graceful shutdown
- **JSON Tests**: Message format validation

Run tests with:
```bash
go test ./internal/service/dex_user -v
```

## Deployment

The service is automatically started when the GraphQL server starts. No additional configuration is required.

### Health Checks

The service health can be monitored through:
- Application logs
- NATS consumer status
- Database connectivity

## Troubleshooting

### Common Issues

1. **NATS Connection Failed**
   - Check NATS server availability
   - Verify connection credentials
   - Check network connectivity

2. **Database Errors**
   - Verify database connection
   - Check table schema
   - Monitor database performance

3. **Message Processing Failures**
   - Check message format
   - Verify user ID format
   - Monitor error logs

### Debug Mode

Enable debug logging by setting log level to DEBUG in configuration.
