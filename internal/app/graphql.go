package app

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	admingraphql "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_error"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/middlewares"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initialize"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
)

type GraphqlServer struct {
	Router     *gin.Engine
	HttpServer *http.Server
	ctx        context.Context
	Cancel     context.CancelFunc
}

func (server *GraphqlServer) Initialize() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		// initializer.Redis()
		// initializer.RedisList()
	}

	// Database tables are managed by Atlas migrations
	// No auto-migration needed

	// Initialize NATS clients - only using nats-meme for now
	// initializer.InitNats() // Temporarily disabled - only using nats-meme for now
	initializer.InitNatsMeme()
	// initializer.InitNatsDex() // Temporarily disabled - not yet implemented for affiliate events

	// Create context for NATS subscribers
	server.ctx, server.Cancel = context.WithCancel(context.Background())

	// Start affiliate subscriber
	if err := initializer.StartAffiliateSubscriber(server.ctx); err != nil {
		global.GVA_LOG.Error("Failed to start affiliate subscriber", zap.Error(err))
	}

	// Start dex user subscriber
	if err := initializer.StartDexUserSubscriber(server.ctx); err != nil {
		global.GVA_LOG.Error("Failed to start dex user subscriber", zap.Error(err))
	}
}

func (server *GraphqlServer) Run() {
	if server.Router == nil {
		server.Router = server.setupRouter()
	}

	address := fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr)
	server.HttpServer = &http.Server{
		Addr:           address,
		Handler:        server.Router,
		ReadTimeout:    20 * time.Second,
		WriteTimeout:   20 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	initializer.InitializeAgentLevels()
	global.GVA_LOG.Info("InitializeAgentLevels started")

	// Initialize Activity Cashback System after database is ready
	if err := initialize.InitializeActivityCashbackSystem(); err != nil {
		global.GVA_LOG.Error("Failed to initialize Activity Cashback System", zap.Error(err))
		// Don't fail the server startup, just log the error
	}

	fmt.Printf(`
	Welcome to XBIT Agent
	Current Version: v1.0.0
	User GraphQL Playground: http://127.0.0.1%s%s/playground
	User GraphQL Endpoint: http://127.0.0.1%s%s
	Admin GraphQL Playground: http://127.0.0.1%s%s/playground
	Admin GraphQL Endpoint: http://127.0.0.1%s%s
`, address, global.GVA_CONFIG.System.GraphqlPrefix, address, global.GVA_CONFIG.System.GraphqlPrefix,
		address, global.GVA_CONFIG.System.AdminGraphqlPrefix, address, global.GVA_CONFIG.System.AdminGraphqlPrefix)

	log.Fatal(server.HttpServer.ListenAndServe())
}

func (server *GraphqlServer) setupRouter() *gin.Engine {
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	router.Use(middlewares.GinGqlContext()) // this will attach gin.Context into graphql context

	// Health check routes (following xbit-goback convention)
	pingHandler := func(c *gin.Context) {
		c.JSON(200, gin.H{
			"pong": time.Now().UnixMilli(),
		})
	}

	// GraphQL API group with prefix
	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)
	api.GET("/ping", pingHandler)
	api.GET("/healthz", pingHandler)

	// Initialize GraphQL servers (User and Admin)
	initUserGraphqlServer(router)
	initAdminGraphqlServer(router)

	return router
}

func initUserGraphqlServer(router *gin.Engine) {
	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)
	// User GraphQL server
	playGroundHandler := playground.Handler("XBIT Agent: User GraphQL playground", fmt.Sprintf("%s", global.GVA_CONFIG.System.GraphqlPrefix))
	graphqlServer := handler.NewDefaultServer(
		graphql.NewExecutableSchema(graphql.Config{
			Resolvers: graphql.NewRootResolver(),
		}),
	)
	graphqlServer.SetErrorPresenter(gql_error.CustomErrorPresenter)

	api.GET("/playground", func(c *gin.Context) {
		playGroundHandler.ServeHTTP(c.Writer, c.Request)
	})

	api.POST("", middlewares.GqlJwtAuth(), func(c *gin.Context) {
		graphqlServer.ServeHTTP(c.Writer, c.Request)
	})
}

func initAdminGraphqlServer(router *gin.Engine) {
	// Admin GraphQL server with different path
	adminPrefix := global.GVA_CONFIG.System.AdminGraphqlPrefix
	adminApi := router.Group(adminPrefix)

	// Admin GraphQL server
	adminPlayGroundHandler := playground.Handler("XBIT Agent: Admin GraphQL playground", adminPrefix)
	adminGraphqlServer := handler.NewDefaultServer(
		admingraphql.NewExecutableSchema(admingraphql.Config{
			Resolvers: admingraphql.NewAdminRootResolver(),
		}),
	)
	adminGraphqlServer.SetErrorPresenter(gql_error.CustomErrorPresenter)

	adminApi.GET("/playground", func(c *gin.Context) {
		adminPlayGroundHandler.ServeHTTP(c.Writer, c.Request)
	})

	adminApi.POST("", middlewares.ApiKeyAuth(), middlewares.GqlJwtAuth(), func(c *gin.Context) {
		adminGraphqlServer.ServeHTTP(c.Writer, c.Request)
	})
}
