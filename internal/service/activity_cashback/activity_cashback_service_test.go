package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// Test cases
func TestActivityCashbackService_InitializeUserForActivityCashback(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	mockService.On("InitializeUserForActivityCashback", ctx, userID).Return(nil)

	err := mockService.InitializeUserForActivityCashback(ctx, userID)

	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_CompleteTask(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()
	verificationData := map[string]interface{}{
		"volume": 100.0,
		"type":   "MEME",
	}

	mockService.On("CompleteTask", ctx, userID, taskID, verificationData).Return(nil)

	err := mockService.CompleteTask(ctx, userID, taskID, verificationData)

	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_GetUserDashboard(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	expectedDashboard := &UserDashboard{
		UserTierInfo: &model.UserTierInfo{
			UserID:      userID,
			CurrentTier: 1,
			TotalPoints: 100,
		},
		PointsToNextTier: 400,
		UserRank:         10,
	}

	mockService.On("GetUserDashboard", ctx, userID).Return(expectedDashboard, nil)

	dashboard, err := mockService.GetUserDashboard(ctx, userID)

	assert.NoError(t, err)
	assert.Equal(t, expectedDashboard, dashboard)
	assert.Equal(t, 1, dashboard.UserTierInfo.CurrentTier)
	assert.Equal(t, 100, dashboard.UserTierInfo.TotalPoints)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_GetTaskCenter(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	expectedTaskCenter := &TaskCenter{
		CompletedToday:    2,
		PointsEarnedToday: 50,
		Categories: []TaskCategoryWithTasks{
			{
				Category: model.TaskCategory{
					ID:          1,
					Name:        "daily",
					DisplayName: "Daily Tasks",
				},
				Tasks: []TaskWithProgress{
					{
						Task: model.ActivityTask{
							ID:     uuid.New(),
							Name:   "Daily Check-in",
							Points: 5,
						},
					},
				},
			},
		},
	}

	mockService.On("GetTaskCenter", ctx, userID).Return(expectedTaskCenter, nil)

	taskCenter, err := mockService.GetTaskCenter(ctx, userID)

	assert.NoError(t, err)
	assert.Equal(t, expectedTaskCenter, taskCenter)
	assert.Equal(t, 2, taskCenter.CompletedToday)
	assert.Equal(t, 50, taskCenter.PointsEarnedToday)
	assert.Len(t, taskCenter.Categories, 1)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_VerifyTradingTask(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	tests := []struct {
		name           string
		tradingData    map[string]interface{}
		expectedResult bool
		expectedError  error
	}{
		{
			name: "Valid MEME trade",
			tradingData: map[string]interface{}{
				"volume":     100.0,
				"trade_type": "MEME",
			},
			expectedResult: true,
			expectedError:  nil,
		},
		{
			name: "Valid Perpetual trade",
			tradingData: map[string]interface{}{
				"volume":     500.0,
				"trade_type": "PERPETUAL",
			},
			expectedResult: true,
			expectedError:  nil,
		},
		{
			name: "Invalid trade - insufficient volume",
			tradingData: map[string]interface{}{
				"volume":     0.5,
				"trade_type": "MEME",
			},
			expectedResult: false,
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService.On("VerifyTradingTask", ctx, userID, taskID, tt.tradingData).Return(tt.expectedResult, tt.expectedError)

			result, err := mockService.VerifyTradingTask(ctx, userID, taskID, tt.tradingData)

			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, tt.expectedError, err)
		})
	}

	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_TaskReset(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()

	tests := []struct {
		name      string
		resetFunc func() error
		mockCall  string
	}{
		{
			name: "Reset Daily Tasks",
			resetFunc: func() error {
				return mockService.ResetDailyTasks(ctx)
			},
			mockCall: "ResetDailyTasks",
		},
		{
			name: "Reset Weekly Tasks",
			resetFunc: func() error {
				return mockService.ResetWeeklyTasks(ctx)
			},
			mockCall: "ResetWeeklyTasks",
		},
		{
			name: "Reset Monthly Tasks",
			resetFunc: func() error {
				return mockService.ResetMonthlyTasks(ctx)
			},
			mockCall: "ResetMonthlyTasks",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService.On(tt.mockCall, ctx).Return(nil)

			err := tt.resetFunc()

			assert.NoError(t, err)
		})
	}

	mockService.AssertExpectations(t)
}

// Integration test helper functions
func createTestTask() *model.ActivityTask {
	return &model.ActivityTask{
		ID:         uuid.New(),
		CategoryID: 1,
		Name:       "Test Task",
		TaskType:   model.TaskTypeDaily,
		Frequency:  model.FrequencyDaily,
		Points:     10,
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
}

func createTestUserTierInfo(userID uuid.UUID) *model.UserTierInfo {
	return &model.UserTierInfo{
		UserID:      userID,
		CurrentTier: 1,
		TotalPoints: 0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

func createTestUserTaskProgress(userID, taskID uuid.UUID) *model.UserTaskProgress {
	return &model.UserTaskProgress{
		ID:            uuid.New(),
		UserID:        userID,
		TaskID:        taskID,
		Status:        model.TaskStatusNotStarted,
		ProgressValue: 0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}
