package infinite

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InfiniteAgentCommissionTask 无限代理收益计算任务
type InfiniteAgentCommissionTask struct{}

// NewInfiniteAgentCommissionTask 创建新的无限代理收益计算任务实例
func NewInfiniteAgentCommissionTask() *InfiniteAgentCommissionTask {
	return &InfiniteAgentCommissionTask{}
}

// CalculateInfiniteAgentCommissions 计算无限代理收益
// 每日0:05分执行，计算所有活跃无限代理的收益
func (t *InfiniteAgentCommissionTask) CalculateInfiniteAgentCommissions() {
	global.GVA_LOG.Info("开始执行无限代理收益计算任务")

	// 获取当前时间作为计算日期
	calculationDate := time.Now().UTC()
	calculationDateStr := calculationDate.Format("2006-01-02 15:04:05")

	global.GVA_LOG.Info("计算日期", zap.String("date", calculationDateStr))

	// 获取所有活跃的无限代理配置
	activeInfiniteAgents, err := t.GetActiveInfiniteAgentConfigs()
	if err != nil {
		global.GVA_LOG.Error("获取活跃无限代理失败", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("找到活跃无限代理", zap.Int("infinite_agent_count", len(activeInfiniteAgents)))

	processedCount := 0
	errorCount := 0

	for _, infiniteAgent := range activeInfiniteAgents {
		if err := t.ProcessInfiniteAgentCommission(infiniteAgent); err != nil {
			global.GVA_LOG.Error("处理无限代理收益计算失败",
				zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("无限代理收益计算任务完成",
		zap.String("date", calculationDateStr),
		zap.Int("total_infinite_agents", len(activeInfiniteAgents)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// GetActiveInfiniteAgentConfigs 获取所有状态为ACTIVE的无限代理配置
func (t *InfiniteAgentCommissionTask) GetActiveInfiniteAgentConfigs() ([]model.InfiniteAgentConfig, error) {
	var infiniteAgents []model.InfiniteAgentConfig

	err := global.GVA_DB.Where("status = ?", "ACTIVE").Find(&infiniteAgents).Error
	if err != nil {
		return nil, fmt.Errorf("查询活跃无限代理失败: %w", err)
	}

	return infiniteAgents, nil
}

// ProcessInfiniteAgentCommission 处理单个无限代理的收益计算
func (t *InfiniteAgentCommissionTask) ProcessInfiniteAgentCommission(infiniteAgent model.InfiniteAgentConfig) error {
	global.GVA_LOG.Debug("开始处理无限代理收益计算",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()))

	// 计算无限代理树的收益
	commissionData, err := t.calculateInfiniteAgentTreeCommission(infiniteAgent.UserID)
	if err != nil {
		return fmt.Errorf("计算无限代理树收益失败: %w", err)
	}

	// 计算最终收益金额
	// 修复：确保净费用不为负值，如果为负值则设为0
	effectiveNetFeeUSD := commissionData.TotalNetFeeUSD
	if effectiveNetFeeUSD.IsNegative() {
		global.GVA_LOG.Warn("检测到负的净费用，将其设为0",
			zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
			zap.String("original_net_fee_usd", commissionData.TotalNetFeeUSD.String()))
		effectiveNetFeeUSD = decimal.Zero
	}

	// 计算无限代理应得的佣金：净费用 * 佣金率
	infiniteAgentCommission := effectiveNetFeeUSD.Mul(infiniteAgent.CommissionRateN)

	// 最终收益 = 无限代理佣金 - 已支付的标准佣金
	// 如果结果小于0，说明已支付的佣金超过了应得佣金，设为0
	finalCommissionAmount := infiniteAgentCommission.Sub(commissionData.TotalStandardCommissionPaidUSD)
	if finalCommissionAmount.IsNegative() {
		global.GVA_LOG.Warn("最终收益为负值，已支付的佣金超过应得佣金，设为0",
			zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
			zap.String("infinite_agent_commission", infiniteAgentCommission.String()),
			zap.String("total_standard_commission_paid_usd", commissionData.TotalStandardCommissionPaidUSD.String()),
			zap.String("calculated_final_amount", finalCommissionAmount.String()))
		finalCommissionAmount = decimal.Zero
	}

	// 更新无限代理配置的收益数据
	err = t.updateInfiniteAgentCommissionData(infiniteAgent.ID, commissionData, finalCommissionAmount)
	if err != nil {
		return fmt.Errorf("更新无限代理收益数据失败: %w", err)
	}

	global.GVA_LOG.Debug("无限代理收益计算完成",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.String("total_net_fee_usd", commissionData.TotalNetFeeUSD.String()),
		zap.String("effective_net_fee_usd", effectiveNetFeeUSD.String()),
		zap.String("infinite_agent_commission", infiniteAgentCommission.String()),
		zap.String("total_standard_commission_paid_usd", commissionData.TotalStandardCommissionPaidUSD.String()),
		zap.String("final_commission_amount_usd", finalCommissionAmount.String()))

	return nil
}

// InfiniteAgentCommissionData 无限代理收益数据结构
type InfiniteAgentCommissionData struct {
	TotalNetFeeUSD                 decimal.Decimal
	TotalStandardCommissionPaidUSD decimal.Decimal
}

// calculateInfiniteAgentTreeCommission 计算无限代理树的收益
func (t *InfiniteAgentCommissionTask) calculateInfiniteAgentTreeCommission(infiniteAgentUserID uuid.UUID) (*InfiniteAgentCommissionData, error) {
	// 查找无限代理用户所在的推荐树快照
	var snapshot model.ReferralTreeSnapshot
	err := global.GVA_DB.Where("infinite_agent_user_id = ? AND is_valid = true", infiniteAgentUserID).
		First(&snapshot).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Warn("未找到无限代理用户的推荐树快照",
				zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))
			return &InfiniteAgentCommissionData{
				TotalNetFeeUSD:                 decimal.Zero,
				TotalStandardCommissionPaidUSD: decimal.Zero,
			}, nil
		}
		return nil, fmt.Errorf("查询推荐树快照失败: %w", err)
	}

	// 获取该树下的所有节点用户ID
	var treeNodeUserIDs []uuid.UUID
	err = global.GVA_DB.Model(&model.ReferralTreeNode{}).
		Where("tree_snapshot_id = ?", snapshot.ID).
		Pluck("user_id", &treeNodeUserIDs).Error
	if err != nil {
		return nil, fmt.Errorf("查询推荐树节点用户ID失败: %w", err)
	}

	if len(treeNodeUserIDs) == 0 {
		global.GVA_LOG.Warn("推荐树下没有节点用户",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
			zap.Uint("snapshot_id", snapshot.ID))
		return &InfiniteAgentCommissionData{
			TotalNetFeeUSD:                 decimal.Zero,
			TotalStandardCommissionPaidUSD: decimal.Zero,
		}, nil
	}

	// 计算总净费用（TotalPerpsFees - (totalPerpsFeesPaid + totalPerpsFeesUnPaid)）
	totalNetFeeUSD, err := t.calculateTotalNetFeeUSD(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("计算总净费用失败: %w", err)
	}

	// 计算已支付的标准佣金总额
	totalStandardCommissionPaidUSD, err := t.calculateTotalStandardCommissionPaidUSD(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("计算已支付标准佣金总额失败: %w", err)
	}

	return &InfiniteAgentCommissionData{
		TotalNetFeeUSD:                 totalNetFeeUSD,
		TotalStandardCommissionPaidUSD: totalStandardCommissionPaidUSD,
	}, nil
}

// calculateTotalNetFeeUSD 计算总净费用
func (t *InfiniteAgentCommissionTask) calculateTotalNetFeeUSD(userIDs []uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalNetFee decimal.Decimal `json:"total_net_fee"`
	}

	// 计算每个用户的净费用：TotalPerpsFees - (totalPerpsFeesPaid + totalPerpsFeesUnPaid)
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	// 构建占位符字符串
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 修复：计算实际可用的净费用
	// 净费用应该是：总费用 - 已支付费用 - 未支付费用
	// 如果结果为负值，说明费用已经被完全支付，净费用为0
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(
			GREATEST(
				rs.total_perps_fees - rs.total_perps_fees_paid - rs.total_perps_fees_un_paid,
				0
			)
		), 0) as total_net_fee
		FROM referral_snapshots rs
		WHERE rs.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(query, args...).Scan(&result).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("查询总净费用失败: %w", err)
	}

	return result.TotalNetFee, nil
}

// calculateTotalStandardCommissionPaidUSD 计算已支付的标准佣金总额
func (t *InfiniteAgentCommissionTask) calculateTotalStandardCommissionPaidUSD(userIDs []uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}

	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	// 构建占位符字符串
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 查询已支付的标准佣金总额
	// 从 ReferralSnapshot 表中查询已支付的佣金
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(rs.total_commission_earned_usd), 0) as total_commission
		FROM referral_snapshots rs
		WHERE rs.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(query, args...).Scan(&result).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("查询已支付标准佣金总额失败: %w", err)
	}

	return result.TotalCommission, nil
}

// updateInfiniteAgentCommissionData 更新无限代理配置的收益数据
func (t *InfiniteAgentCommissionTask) updateInfiniteAgentCommissionData(
	configID uuid.UUID,
	commissionData *InfiniteAgentCommissionData,
	finalCommissionAmount decimal.Decimal,
) error {
	// 计算最终佣金金额
	finalCommission := finalCommissionAmount

	// 更新数据库
	updates := map[string]interface{}{
		"total_net_fee_usd":                  commissionData.TotalNetFeeUSD,
		"total_standard_commission_paid_usd": commissionData.TotalStandardCommissionPaidUSD,
		"final_commission_amount_usd":        finalCommission,
		"updated_at":                         time.Now().UTC(),
	}

	err := global.GVA_DB.Model(&model.InfiniteAgentConfig{}).
		Where("id = ?", configID).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新无限代理收益数据失败: %w", err)
	}

	return nil
}
