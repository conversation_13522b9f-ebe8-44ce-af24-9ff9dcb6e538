package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// ConsumeUserLoginEvent processes user login events for daily check-in
func ConsumeUserLoginEvent(msg *nats.Msg) error {
	logger := global.GVA_LOG

	var event UserLoginEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		logger.Error("failed to unmarshal user login event", zap.Error(err))
		return fmt.Errorf("failed to unmarshal user login event: %v", err)
	}

	logger.Info("Received UserLoginEvent",
		zap.String("user_id", event.UserID),
		zap.String("login_method", event.LoginMethod),
		zap.String("timestamp", event.Timestamp))

	// Parse timestamp
	timestamp, err := time.Parse(time.RFC3339, event.Timestamp)
	if err != nil {
		logger.Error("failed to parse timestamp", zap.Error(err), zap.String("timestamp", event.Timestamp))
		// Continue processing even if timestamp parsing fails
		timestamp = time.Now()
	}

	// Get global system initializer to access background job manager
	globalSystemInitializer := activity_cashback.GetGlobalSystemInitializer()
	if globalSystemInitializer == nil {
		logger.Warn("Activity Cashback system not initialized, skipping login processing")
		return nil
	}

	backgroundJobManager := globalSystemInitializer.GetBackgroundJobManager()
	if backgroundJobManager == nil {
		logger.Warn("Background job manager not available, skipping login processing")
		return nil
	}

	// Process daily check-in for the user
	ctx := context.Background()
	if err := backgroundJobManager.ProcessUserLogin(ctx, event.UserID); err != nil {
		logger.Error("failed to process user login for daily check-in",
			zap.String("user_id", event.UserID),
			zap.Error(err))
		return fmt.Errorf("failed to process user login: %v", err)
	}

	logger.Info("Successfully processed user login for daily check-in",
		zap.String("user_id", event.UserID),
		zap.Time("login_time", timestamp))

	return nil
}
