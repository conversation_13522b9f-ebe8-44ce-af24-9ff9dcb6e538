package test

import (
	"os"
	"path/filepath"
	"runtime"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestConfig holds test-specific configuration
type TestConfig struct {
	*config.Server
}

// SetupTestConfig initializes test configuration
func SetupTestConfig() *TestConfig {
	// Get the project root directory
	_, filename, _, _ := runtime.Caller(0)
	projectRoot := filepath.Dir(filepath.Dir(filepath.Dir(filename)))

	testConfig := &TestConfig{
		Server: &config.Server{
			JWT: config.JWT{
				SigningKey:  "test-secret-key-for-unit-tests",
				ExpiresTime: "24h",
				BufferTime:  "1h",
				Issuer:      "xbit-agent-test",
			},
			System: config.System{
				Env:           "test",
				Addr:          8080,
				DbType:        "sqlite",
				OssType:       "local",
				UseMultipoint: false,
				UseRedis:      false,
				LimitCountIP:  15000,
				LimitTimeIP:   3600,
			},
			Zap: config.Zap{
				Level:         "info",
				Prefix:        "[xbit-agent-test]",
				Format:        "console",
				Director:      filepath.Join(projectRoot, "test", "logs"),
				EncodeLevel:   "LowercaseColorLevelEncoder",
				StacktraceKey: "stacktrace",
				LogInConsole:  true,
			},
			Redis: config.Redis{
				DB:       0,
				Addr:     "127.0.0.1:6379",
				Password: "",
			},
			Pgsql: config.Pgsql{
				GeneralDB: config.GeneralDB{
					Path:         ":memory:",
					Port:         "5432",
					Config:       "charset=utf8mb4&parseTime=True&loc=Local",
					Dbname:       "test_db",
					Username:     "test",
					Password:     "test",
					MaxIdleConns: 10,
					MaxOpenConns: 100,
					LogMode:      "info",
					LogZap:       true,
				},
			},
			NatsMeme: config.Nats{
				URL: "nats://localhost:4222",
			},
			NatsDex: config.Nats{
				URL: "nats://localhost:4222",
			},
		},
	}

	// Set global config for tests
	global.GVA_CONFIG = *testConfig.Server

	// Setup test logger
	setupTestLogger()

	return testConfig
}

// setupTestLogger initializes a test-friendly logger
func setupTestLogger() {
	// Create logs directory if it doesn't exist
	if err := os.MkdirAll(global.GVA_CONFIG.Zap.Director, 0755); err != nil {
		panic(err)
	}

	// Configure logger for tests
	config := zap.NewDevelopmentConfig()
	config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	config.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	global.GVA_LOG = logger
}

// SetupTestDB initializes an in-memory SQLite database for testing
func SetupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		panic("Failed to connect to test database: " + err.Error())
	}

	// Set the global database for tests
	global.GVA_DB = db

	// Auto-migrate the activity cashback models
	if err := migrateActivityCashbackModels(db); err != nil {
		panic("Failed to migrate activity cashback models: " + err.Error())
	}

	return db
}

// CleanupTestDB cleans up the test database
func CleanupTestDB() {
	if global.GVA_DB != nil {
		sqlDB, err := global.GVA_DB.DB()
		if err == nil {
			sqlDB.Close()
		}
		global.GVA_DB = nil
	}
}

// CleanupTestConfig cleans up test resources
func CleanupTestConfig() {
	if global.GVA_LOG != nil {
		global.GVA_LOG.Sync()
	}
	CleanupTestDB()
}

// migrateActivityCashbackModels creates minimal tables for activity cashback tests
func migrateActivityCashbackModels(db *gorm.DB) error {
	// Create minimal tables manually to avoid PostgreSQL-specific syntax issues
	tables := []string{
		`CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY,
			email TEXT UNIQUE,
			invitation_code TEXT,
			agent_level_id INTEGER DEFAULT 1,
			level_grace_period_started_at DATETIME,
			level_upgraded_at DATETIME,
			first_transaction_at DATETIME,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME
		)`,
		`CREATE TABLE IF NOT EXISTS agent_levels (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			min_volume_usd DECIMAL(20,8) DEFAULT 0,
			min_referrals INTEGER DEFAULT 0,
			commission_percentage DECIMAL(5,4) DEFAULT 0,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS task_categories (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT UNIQUE NOT NULL,
			display_name TEXT NOT NULL,
			description TEXT,
			icon TEXT,
			sort_order INTEGER DEFAULT 0,
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS tier_benefits (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			tier_level INTEGER UNIQUE NOT NULL,
			tier_name TEXT NOT NULL,
			min_points INTEGER DEFAULT 0,
			cashback_percentage DECIMAL(5,4) DEFAULT 0,
			benefits_description TEXT,
			tier_color TEXT,
			tier_icon TEXT,
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS activity_tasks (
			id TEXT PRIMARY KEY,
			category_id INTEGER NOT NULL,
			name TEXT NOT NULL,
			description TEXT,
			task_type TEXT NOT NULL,
			frequency TEXT NOT NULL,
			task_identifier TEXT,
			points INTEGER DEFAULT 0,
			max_completions INTEGER,
			reset_period TEXT,
			conditions TEXT,
			action_target TEXT,
			verification_method TEXT,
			external_link TEXT,
			is_active BOOLEAN DEFAULT 1,
			start_date DATETIME,
			end_date DATETIME,
			sort_order INTEGER DEFAULT 0,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			created_by TEXT,
			updated_by TEXT
		)`,
		`CREATE TABLE IF NOT EXISTS user_tier_info (
			user_id TEXT PRIMARY KEY,
			current_tier INTEGER DEFAULT 1,
			total_points INTEGER DEFAULT 0,
			points_this_month INTEGER DEFAULT 0,
			trading_volume_usd DECIMAL(38,2) DEFAULT 0,
			active_days_this_month INTEGER DEFAULT 0,
			cumulative_cashback_usd DECIMAL(38,6) DEFAULT 0,
			claimable_cashback_usd DECIMAL(38,6) DEFAULT 0,
			claimed_cashback_usd DECIMAL(38,6) DEFAULT 0,
			last_activity_date DATETIME,
			tier_upgraded_at DATETIME,
			monthly_reset_at DATETIME,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS user_task_progress (
			id TEXT PRIMARY KEY,
			user_id TEXT NOT NULL,
			task_id TEXT NOT NULL,
			status TEXT DEFAULT 'NOT_STARTED',
			progress_value INTEGER DEFAULT 0,
			target_value INTEGER,
			completion_count INTEGER DEFAULT 0,
			points_earned INTEGER DEFAULT 0,
			last_completed_at DATETIME,
			last_reset_at DATETIME,
			streak_count INTEGER DEFAULT 0,
			metadata TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS task_completion_history (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			user_id TEXT NOT NULL,
			task_id TEXT NOT NULL,
			points_earned INTEGER DEFAULT 0,
			verification_data TEXT,
			completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS activity_cashback_claims (
			id TEXT PRIMARY KEY,
			user_id TEXT NOT NULL,
			claim_type TEXT NOT NULL,
			total_amount_usd DECIMAL(20,8) NOT NULL,
			total_amount_sol DECIMAL(20,8) NOT NULL,
			transaction_hash TEXT,
			status TEXT DEFAULT 'PENDING',
			claimed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			processed_at DATETIME,
			metadata TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS pending_community_tasks (
			id TEXT PRIMARY KEY,
			user_id TEXT NOT NULL,
			task_id TEXT NOT NULL,
			status TEXT DEFAULT 'PENDING',
			clicked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			completion_time DATETIME,
			actual_completed_at DATETIME,
			verification_data TEXT,
			ip_address TEXT,
			user_agent TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_id) REFERENCES users(id),
			FOREIGN KEY (task_id) REFERENCES activity_tasks(id)
		)`,
		`CREATE TABLE IF NOT EXISTS one_time_task_completions (
			id TEXT PRIMARY KEY,
			user_id TEXT NOT NULL,
			task_id TEXT NOT NULL,
			points_awarded INTEGER DEFAULT 0,
			completion_date DATETIME DEFAULT CURRENT_TIMESTAMP,
			verification_data TEXT,
			ip_address TEXT,
			user_agent TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(user_id, task_id)
		)`,
	}

	for _, table := range tables {
		if err := db.Exec(table).Error; err != nil {
			return err
		}
	}

	// Insert default tier benefits
	seedData := []string{
		`INSERT OR IGNORE INTO tier_benefits (tier_level, tier_name, min_points, cashback_percentage, benefits_description, tier_color, is_active) VALUES
			(1, 'Bronze', 0, 0.0010, 'Basic tier with 0.1% cashback', '#CD7F32', 1),
			(2, 'Silver', 1000, 0.0015, 'Silver tier with 0.15% cashback', '#C0C0C0', 1),
			(3, 'Gold', 5000, 0.0020, 'Gold tier with 0.2% cashback', '#FFD700', 1),
			(4, 'Platinum', 15000, 0.0025, 'Platinum tier with 0.25% cashback', '#E5E4E2', 1),
			(5, 'Diamond', 50000, 0.0030, 'Diamond tier with 0.3% cashback', '#B9F2FF', 1)`,
		`INSERT OR IGNORE INTO agent_levels (id, name, min_volume_usd, min_referrals, commission_percentage) VALUES
			(1, 'Starter', 0, 0, 0.0500),
			(2, 'Bronze', 10000, 5, 0.0750),
			(3, 'Silver', 50000, 15, 0.1000),
			(4, 'Gold', 200000, 50, 0.1250),
			(5, 'Platinum', 1000000, 150, 0.1500)`,
	}

	for _, seed := range seedData {
		if err := db.Exec(seed).Error; err != nil {
			return err
		}
	}

	return nil
}
